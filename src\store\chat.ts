import { defineStore } from 'pinia';
import type { ChatSession, ChatSessionMetadata } from '@/types/chat';
import { chatApiService } from '@/services/apiService';

interface ChatState {
  currentChatId: string | null;
  chatList: ChatSessionMetadata[];
  isLoading: boolean;
  error: string | null;
}

// 时间分组接口
export interface ChatGroup {
  title: string;
  items: ChatSessionMetadata[];
}

/// @ts-expect-error
export const useChatStore = defineStore('chat', {
  state: () => ({
    currentChatId: null,
    chatList: [],
    isLoading: false,
    error: null
  } as ChatState),

  getters: {
    getCurrentChatId: (state) => state.currentChatId,
    getChatList: (state) => state.chatList,
    isCurrentlyLoading: (state) => state.isLoading,
    getError: (state) => state.error,

    // 按时间分组的对话列表
    getGroupedChatList: (state): ChatGroup[] => {
      const now = new Date();
      const sevenDaysAgo = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000);

      const recent: ChatSessionMetadata[] = [];
      const older: ChatSessionMetadata[] = [];

      state.chatList.forEach(chat => {
        const chatDate = new Date(chat.updated_at);
        if (chatDate >= sevenDaysAgo) {
          recent.push(chat);
        } else {
          older.push(chat);
        }
      });

      const groups: ChatGroup[] = [];
      if (recent.length > 0) {
        groups.push({ title: '7 天内', items: recent });
      }
      if (older.length > 0) {
        groups.push({ title: '更早', items: older });
      }

      return groups;
    }
  },

  actions: {
    setCurrentChatId(chatId: string | null) {
      this.currentChatId = chatId;
    },

    setChatList(chatList: ChatSessionMetadata[]) {
      this.chatList = chatList;
    },

    addChat(chat: ChatSessionMetadata) {
      this.chatList.unshift(chat); // Add to beginning
    },

    removeChat(chatId: string) {
      this.chatList = this.chatList.filter(chat => chat.id !== chatId);
    },

    updateChatTitle(chatId: string, newTitle: string) {
      const chat = this.chatList.find(chat => chat.id === chatId);
      if (chat) {
        chat.title = newTitle;
        // 更新时间戳
        chat.updated_at = new Date().toISOString();
      }
    },

    setLoading(loading: boolean) {
      this.isLoading = loading;
    },

    setError(error: string | null) {
      this.error = error;
    },

    clearCurrentChat() {
      this.currentChatId = null;
    },

    // 获取对话列表
    async fetchChatList() {
      this.setLoading(true);
      this.setError(null);
      try {
        const chatList = await chatApiService.getChats();
        this.setChatList(chatList);
      } catch (error) {
        console.error('Failed to fetch chat list:', error);
        this.setError('获取对话列表失败');
      } finally {
        this.setLoading(false);
      }
    },

    // 创建新对话
    async createNewChat(title: string = '新对话'): Promise<ChatSession | null> {
      this.setLoading(true);
      this.setError(null);
      try {
        const newChat = await chatApiService.createChat(title);

        // 添加到列表
        const chatMetadata: ChatSessionMetadata = {
          id: newChat.id,
          title: newChat.title || title,
          created_at: newChat.creation_timestamp,
          updated_at: newChat.last_update_timestamp,
          entry_node_id: newChat.entry_node_id
        };
        this.addChat(chatMetadata);

        // 设置为当前对话
        this.setCurrentChatId(newChat.id);

        return newChat;
      } catch (error) {
        console.error('Failed to create new chat:', error);
        this.setError('创建新对话失败');
        return null;
      } finally {
        this.setLoading(false);
      }
    },

    // 删除对话
    async deleteChatById(chatId: string): Promise<boolean> {
      this.setLoading(true);
      this.setError(null);
      try {
        await chatApiService.deleteChat(chatId);

        // 从列表中移除
        this.removeChat(chatId);

        // 如果删除的是当前对话，清除当前对话ID
        if (this.currentChatId === chatId) {
          this.clearCurrentChat();
        }

        return true;
      } catch (error) {
        console.error('Failed to delete chat:', error);
        this.setError('删除对话失败');
        return false;
      } finally {
        this.setLoading(false);
      }
    },

    // 重命名对话
    async renameChatById(chatId: string, newTitle: string): Promise<boolean> {
      if (!newTitle.trim() || newTitle.length > 150) {
        this.setError('标题长度必须在1-150字符之间');
        return false;
      }

      this.setLoading(true);
      this.setError(null);
      try {
        await chatApiService.renameChat(chatId, newTitle.trim());

        // 更新本地状态
        this.updateChatTitle(chatId, newTitle.trim());

        return true;
      } catch (error) {
        console.error('Failed to rename chat:', error);
        this.setError('重命名对话失败');
        return false;
      } finally {
        this.setLoading(false);
      }
    }
  },

  persist: {
    paths: ['currentChatId'] // Only persist current chat ID
  }
});
