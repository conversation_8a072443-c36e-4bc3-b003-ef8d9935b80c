import { defineStore } from 'pinia';
import type { ChatSession, ChatSessionMetadata } from '@/types/chat';

interface ChatState {
  currentChatId: string | null;
  chatList: ChatSessionMetadata[];
  isLoading: boolean;
}

/// @ts-expect-error
export const useChatStore = defineStore('chat', {
  state: () => ({
    currentChatId: null,
    chatList: [],
    isLoading: false
  } as ChatState),
  
  getters: {
    getCurrentChatId: (state) => state.currentChatId,
    getChatList: (state) => state.chatList,
    isCurrentlyLoading: (state) => state.isLoading
  },
  
  actions: {
    setCurrentChatId(chatId: string | null) {
      this.currentChatId = chatId;
    },
    
    setChatList(chatList: ChatSessionMetadata[]) {
      this.chatList = chatList;
    },
    
    addChat(chat: ChatSessionMetadata) {
      this.chatList.unshift(chat); // Add to beginning
    },
    
    removeChat(chatId: string) {
      this.chatList = this.chatList.filter(chat => chat.id !== chatId);
    },
    
    updateChatTitle(chatId: string, newTitle: string) {
      const chat = this.chatList.find(chat => chat.id === chatId);
      if (chat) {
        chat.title = newTitle;
      }
    },
    
    setLoading(loading: boolean) {
      this.isLoading = loading;
    },
    
    clearCurrentChat() {
      this.currentChatId = null;
    }
  },
  
  persist: {
    paths: ['currentChatId'] // Only persist current chat ID
  }
});
