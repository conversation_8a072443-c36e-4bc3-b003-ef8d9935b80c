// src/services/apiService.ts

import type { 
  ChatSession, 
  ChatSessionMetadata, 
  ChatMessageNode, 
  MessageTurnPayload, 
  MessageTurnResponse 
} from '@/types/chat';

const API_BASE_URL = '/api'; // Adjust if your Flask prefix is different

async function fetchApi<T>(url: string, options: RequestInit = {}): Promise<T> {
  const headers = {
    'Content-Type': 'application/json',
    // Add Authorization header if needed, e.g., for JWT
    // 'Authorization': `Bearer ${localStorage.getItem('authToken')}`,
    ...options.headers,
  };
  const response = await fetch(`${API_BASE_URL}${url}`, { ...options, headers });
  if (!response.ok) {
    const errorData = await response.json().catch(() => ({ message: response.statusText }));
    throw new Error(`API Error (${response.status}): ${errorData.message || 'Unknown error'}`);
  }
  if (response.status === 204) { // No Content
    return undefined as T;
  }
  return response.json() as T;
}

export const chatApiService = {
  createChat: (title: string = 'New Chat'): Promise<ChatSession> => {
    return fetchApi<ChatSession>('/chats', {
      method: 'POST',
      body: JSON.stringify({ title }),
    });
  },

  getChats: (): Promise<ChatSessionMetadata[]> => {
    return fetchApi<ChatSessionMetadata[]>('/chats');
  },

  getChatDetails: (chatId: string): Promise<ChatSession> => {
    return fetchApi<ChatSession>(`/chats/${chatId}`);
  },

  deleteChat: (chatId: string): Promise<{ message: string }> => {
    return fetchApi<{ message: string }>(`/chats/${chatId}`, {
      method: 'DELETE',
    });
  },

  renameChat: (chatId: string, newTitle: string): Promise<{ message: string, id: string, new_title: string }> => {
    return fetchApi<{ message: string, id: string, new_title: string }>(`/chats/${chatId}/rename`, {
      method: 'PUT',
      body: JSON.stringify({ title: newTitle }),
    });
  },

  postMessageTurn: (chatId: string, payload: MessageTurnPayload): Promise<MessageTurnResponse> => {
    return fetchApi<MessageTurnResponse>(`/chats/${chatId}/message_turns`, {
      method: 'POST',
      body: JSON.stringify(payload),
    });
  },

  setActiveBranch: (chatId: string, nodeId: string, activeNextNodeId: string): Promise<{ message: string, updated_node_id: string, new_active_next_node_id: string }> => {
    return fetchApi<{ message: string, updated_node_id: string, new_active_next_node_id: string }>(`/chats/${chatId}/nodes/${nodeId}/set_active_branch`, {
      method: 'PUT',
      body: JSON.stringify({ active_next_node_id: activeNextNodeId }),
    });
  },
};
