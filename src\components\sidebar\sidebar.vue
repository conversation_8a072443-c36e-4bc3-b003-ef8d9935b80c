<script setup>
import {nextTick, ref} from "vue";
import SidebarExpanded from "@/components/sidebar/sidebar-expanded.vue";
import SidebarFolded from "@/components/sidebar/sidebar-folded.vue";

const expanded = ref(true);
const transformX = ref(0);
const transitionEnabled = ref(true);
const TRANSITION_TIME = 300;

const sleep = (ms) => new Promise(resolve => window.setTimeout(resolve, ms));

// 展开收缩动画
async function toggle() {
  // 收缩
  if (expanded.value) {
    transformX.value = -260; // SidebarExpanded 宽度
    await sleep(TRANSITION_TIME);
    transformX.value = 0;
    expanded.value = false;
  } 
  // 展开
  else {
    // 先收起来 SidebarFolded
    transformX.value = -68; // SidebarFolded 宽度
    await sleep(TRANSITION_TIME);
    transitionEnabled.value = false;
    // 展开 SidebarExpanded
    transformX.value = -260;
    await nextTick();
    transitionEnabled.value = true;
    transformX.value = 0;
    expanded.value = true;
  }
}
</script>

<template>
  <div id="sidebar-container" 
       :class="{ 'transition-enabled': transitionEnabled }"
       :style="{ transform: `translateX(${transformX}px)` }">
    <SidebarExpanded v-if="expanded" @toggle="toggle" />
    <SidebarFolded v-else @toggle="toggle" />
  </div>
</template>

<style scoped lang="scss">
$transition-speed: 0.3s;
$transition-timing: cubic-bezier(0.4, 0, 0.2, 1);

#sidebar-container {
  height: 100%;
  
  &.transition-enabled {
    transition: transform $transition-speed $transition-timing, opacity $transition-speed $transition-timing;
  }
}
</style>